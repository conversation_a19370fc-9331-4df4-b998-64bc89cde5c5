#!/usr/bin/env python3
"""
重启检测脚本 - check_restarts.py

功能：从Prometheus获取设备uptime数据，智能检测真实重启和数据异常
作者：AI Assistant
版本：1.0.0
"""

import sys
import json
import re
import argparse
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

try:
    import requests
    import pendulum
except ImportError as e:
    print(f"❌ 错误：缺少必要依赖: {e}")
    print("请执行: pip install -r requirements.txt")
    sys.exit(4)


# ================================
# 数据结构定义
# ================================

@dataclass
class Device:
    """设备信息"""
    name: str           # 设备名称
    sn: str             # 设备序列号


@dataclass 
class DataPoint:
    """时序数据点"""
    timestamp: datetime  # 时间戳
    value: float        # uptime值


@dataclass
class RestartEvent:
    """重启事件"""
    timestamp: datetime     # 事件发生时间
    event_type: str        # "restart" | "anomaly"
    uptime_before: float   # 下降前uptime
    uptime_after: float    # 下降后uptime
    recovery_uptime: Optional[float] = None  # 恢复后uptime（异常事件）


@dataclass
class MemoryStats:
    """内存统计结果"""
    min_memory: Optional[float]     # 最小内存值(bytes)
    max_memory: Optional[float]     # 最大内存值(bytes)
    avg_memory: Optional[float]     # 平均内存值(bytes)
    memory_count: int               # 内存数据点数量

    def format_stats(self) -> str:
        """格式化内存统计为显示字符串"""
        if self.memory_count == 0:
            return "-"

        min_str = format_memory_size(self.min_memory)
        max_str = format_memory_size(self.max_memory)
        avg_str = format_memory_size(self.avg_memory)
        return f"{min_str}/{max_str}/{avg_str}"


@dataclass
class DeviceMemoryStats:
    """设备内存统计结果（包含重启前后）"""
    before_restart: MemoryStats     # 重启前内存统计
    after_restart: MemoryStats      # 重启后内存统计


@dataclass
class DeviceResult:
    """设备检测结果"""
    device: Device
    restart_count: int
    anomaly_count: int
    last_restart_time: Optional[datetime]
    events: List[RestartEvent]
    memory_stats: DeviceMemoryStats  # 重启前后内存统计结果
    gateway_restarted: Optional[bool] = None  # 网关重启状态（仅对有重启的设备）
    extend_data: Optional[List[str]] = None  # 扩展列数据（按配置顺序）


@dataclass
class RecoveryPattern:
    """恢复模式分析结果"""
    is_anomaly: bool
    final_uptime: float
    recovery_ratio: float = 0.0
    growth_rate: float = 0.0
    is_restart: bool = False


@dataclass
class FormattedResult:
    """格式化结果"""
    device_table: str                    # 设备检测结果表格（包含内存统计和网关重启状态）
    summary_info: str                    # 汇总统计信息（包含网关重启统计）
    time_range: str                      # 时间范围信息
    csv_file_path: Optional[str] = None  # CSV文件路径（如果导出了CSV）


# ================================
# 错误处理和工具函数
# ================================

class CheckRestartsError(Exception):
    """自定义异常基类"""
    pass


class PrometheusConnectionError(CheckRestartsError):
    """Prometheus连接错误"""
    pass


class DataFormatError(CheckRestartsError):
    """数据格式错误"""
    pass


class ParameterError(CheckRestartsError):
    """参数错误"""
    pass


def exit_with_error(message: str, code: int = 1):
    """统一错误退出"""
    print(f"❌ 错误：{message}")
    sys.exit(code)


def print_info(message: str):
    """打印信息"""
    print(f"ℹ️  {message}")


def print_success(message: str):
    """打印成功信息"""
    print(f"✅ {message}")


def format_memory_size(bytes_value: float) -> str:
    """格式化内存大小，自动选择合适单位(KB/MB)"""
    if bytes_value is None:
        return "0B"

    if bytes_value < 1024:
        return f"{bytes_value:.1f}B"
    elif bytes_value < 1024 * 1024:
        return f"{bytes_value/1024:.1f}KB"
    else:
        return f"{bytes_value/1024/1024:.1f}MB"


# ================================
# Prometheus客户端
# ================================

class PrometheusClient:
    """Prometheus数据获取客户端"""
    
    def __init__(self, base_url: str, timeout: int = 10):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.timeout = timeout
    
    def test_connection(self) -> bool:
        """测试Prometheus连接"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/status/config", timeout=5)
            return response.status_code == 200
        except (requests.ConnectionError, requests.Timeout):
            return False
        except Exception:
            return False
    
    def get_devices(self, name_pattern: str) -> List[Device]:
        """通过正则匹配获取设备列表"""
        try:
            # 查询所有dev_mem_ustest指标，获取设备信息
            query = 'dev_mem_ustest'
            params = {'query': query}
            
            response = self.session.get(
                f"{self.base_url}/api/v1/query",
                params=params,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            if data['status'] != 'success':
                raise DataFormatError(f"Prometheus查询失败: {data.get('error', '未知错误')}")
            
            # 收集唯一的设备信息
            device_map = {}
            for result in data['data']['result']:
                metric = result['metric']
                name = metric.get('name', '')
                sn = metric.get('sn', '')
                
                # 正则匹配设备名称
                if name and sn and re.search(name_pattern, name):
                    device_key = f"{name}:{sn}"
                    if device_key not in device_map:
                        device_map[device_key] = Device(name=name, sn=sn)
            
            devices = list(device_map.values())
            return devices
            
        except requests.exceptions.RequestException as e:
            if "400" in str(e):
                raise DataFormatError(f"Prometheus查询语法错误，可能是指标 'dev_mem_ustest' 不存在: {e}")
            else:
                raise PrometheusConnectionError(f"网络请求失败: {e}")
        except (KeyError, ValueError) as e:
            raise DataFormatError(f"响应数据格式错误: {e}")
    
    def get_uptime_data(self, devices: List[Device], start_time: datetime, end_time: datetime,
                       name_pattern: str, step: str = None) -> Dict[str, List[DataPoint]]:
        """批量获取设备uptime数据"""
        if not devices:
            return {}

        try:
            # 直接使用原始正则表达式，避免URL过长问题
            query = f'dev_mem_ustest{{name=~"{name_pattern}",type="uptime"}}'

            # 自动计算合适的步长，避免超过11000点限制
            if step is None:
                duration_seconds = (end_time - start_time).total_seconds()
                # 目标是不超过10000个数据点，留一些余量
                step_seconds = max(60, int(duration_seconds / 10000))
                step = f"{step_seconds}s"

            params = {
                'query': query,
                'start': start_time.timestamp(),
                'end': end_time.timestamp(),
                'step': step
            }
            
            response = self.session.get(
                f"{self.base_url}/api/v1/query_range",
                params=params,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            if data['status'] != 'success':
                raise DataFormatError(f"Prometheus查询失败: {data.get('error', '未知错误')}")
            
            # 解析时序数据，只处理匹配的设备
            device_names = {device.name for device in devices}
            device_data = {}
            for result in data['data']['result']:
                metric = result['metric']
                device_name = metric.get('name', '')

                # 只处理在设备列表中的设备
                if device_name and device_name in device_names:
                    datapoints = []
                    for timestamp, value in result['values']:
                        try:
                            dt = datetime.fromtimestamp(float(timestamp))
                            val = float(value)
                            datapoints.append(DataPoint(timestamp=dt, value=val))
                        except (ValueError, TypeError):
                            continue  # 跳过无效数据点

                    # 按时间排序
                    datapoints.sort(key=lambda x: x.timestamp)
                    device_data[device_name] = datapoints
            
            return device_data
            
        except requests.exceptions.RequestException as e:
            raise PrometheusConnectionError(f"网络请求失败: {e}")
        except (KeyError, ValueError) as e:
            raise DataFormatError(f"响应数据格式错误: {e}")

    def get_memory_data(self, devices: List[Device], start_time: datetime, end_time: datetime,
                       name_pattern: str, step: str = None) -> Dict[str, List[DataPoint]]:
        """批量获取设备内存占用数据（单位：KB，需转换为bytes）"""
        if not devices:
            return {}

        try:
            # 直接使用原始正则表达式，避免URL过长问题
            query = f'dev_mem_ustest{{name=~"{name_pattern}",type="inner"}}'

            # 自动计算合适的步长，避免超过11000点限制
            if step is None:
                duration_seconds = (end_time - start_time).total_seconds()
                # 目标是不超过10000个数据点，留一些余量
                step_seconds = max(60, int(duration_seconds / 10000))
                step = f"{step_seconds}s"

            params = {
                'query': query,
                'start': start_time.timestamp(),
                'end': end_time.timestamp(),
                'step': step
            }

            response = self.session.get(
                f"{self.base_url}/api/v1/query_range",
                params=params,
                timeout=self.timeout
            )
            response.raise_for_status()

            data = response.json()
            if data['status'] != 'success':
                raise DataFormatError(f"Prometheus查询失败: {data.get('error', '未知错误')}")

            # 解析时序数据，只处理匹配的设备
            device_names = {device.name for device in devices}
            device_data = {}
            for result in data['data']['result']:
                metric = result['metric']
                device_name = metric.get('name', '')

                # 只处理在设备列表中的设备
                if device_name and device_name in device_names:
                    datapoints = []
                    for timestamp, value in result['values']:
                        try:
                            dt = datetime.fromtimestamp(float(timestamp))
                            # 内存数据从KB转换为bytes
                            val = float(value) * 1024
                            datapoints.append(DataPoint(timestamp=dt, value=val))
                        except (ValueError, TypeError):
                            continue  # 跳过无效数据点

                    # 按时间排序
                    datapoints.sort(key=lambda x: x.timestamp)
                    device_data[device_name] = datapoints

            return device_data

        except requests.exceptions.RequestException as e:
            raise PrometheusConnectionError(f"网络请求失败: {e}")
        except (KeyError, ValueError) as e:
            raise DataFormatError(f"响应数据格式错误: {e}")


# ================================
# 网关客户端
# ================================

class GatewayClient:
    """网关数据获取客户端"""
    
    def __init__(self, base_url: str = "http://************:21000", timeout: int = 10):
        self.base_url = base_url.rstrip('/')  # 移除末尾的斜杠
        self.timeout = timeout
        
    def test_connection(self) -> bool:
        """测试网关连接"""
        try:
            # 使用一个简单的测试SN来验证连接
            test_sn = "0123456789AB"
            self.get_gateway_uptime(test_sn)
            return True
        except Exception:
            return False
    
    def get_gateway_uptime(self, device_sn: str) -> float:
        """获取网关sys_uptime（基于ctgw_long.py的systime函数）"""
        url = f"{self.base_url}/api/v1/diagnose/do"
        payload = {
            "sn": device_sn,
            "cmd": {
                "subtype": "shell", 
                "action": "read", 
                "args": "cat /proc/uptime"
            }
        }
        
        try:
            response = requests.post(url, json=payload, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析响应，去掉外层引号
            response_text = response.text.strip('"')
            
            # 解析格式: "362138.80 665239.27\n"
            parts = response_text.split()
            if len(parts) < 1:
                raise DataFormatError(f"网关响应格式错误，期望至少1个数字，得到: {response_text}")
            
            uptime_str = parts[0]  # 取第一个数字
            uptime = float(uptime_str)
            
            if uptime < 0:
                raise DataFormatError(f"网关uptime值异常: {uptime}，应为正数")
                
            return uptime
            
        except requests.exceptions.RequestException as e:
            raise PrometheusConnectionError(f"网关连接失败: {e}")
        except (ValueError, IndexError) as e:
            raise DataFormatError(f"网关响应解析失败: {e}")
    
    def get_plugin_uptime(self, device_sn: str) -> float:
        """获取插件plg_uptime（基于ctgw_long.py的get_uptime函数）"""
        url = f"{self.base_url}/api/v1/diagnose/do"
        payload = {
            "sn": device_sn,
            "cmd": {
                "subtype": "sys",
                "action": "dpi_uptime"
            }
        }
        
        try:
            response = requests.post(url, json=payload, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析响应，去掉外层引号
            uptime_text = response.text.strip('"')
            
            # 检查是否有错误
            if "error" in uptime_text.lower():
                raise DataFormatError(f"插件uptime获取错误: {uptime_text}")
            
            uptime = float(uptime_text)
            
            if uptime < 0:
                raise DataFormatError(f"插件uptime值异常: {uptime}，应为正数")
                
            return uptime
            
        except requests.exceptions.RequestException as e:
            raise PrometheusConnectionError(f"网关连接失败: {e}")
        except (ValueError, TypeError) as e:
            raise DataFormatError(f"插件uptime解析失败: {e}")


# ================================
# 扩展列数据获取客户端
# ================================

class ExtendColumnClient:
    """扩展列数据获取客户端（基于验证的网关接口）"""

    def __init__(self, base_url: str = "http://************:21000", timeout: int = 10):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout

    def get_extend_column_data(self, device_sn: str, extend_config: Dict) -> str:
        """
        获取单个扩展列的数据（基于验证的实现）

        Args:
            device_sn: 设备序列号
            extend_config: 扩展列配置

        Returns:
            str: 扩展列数据值，失败返回"ERROR"，字段不存在返回"-"
        """
        url = f"{self.base_url}/api/v1/diagnose/do"

        payload = {
            "sn": device_sn,
            "cmd": {
                "subtype": extend_config["subtype"],
                "action": extend_config["action"]
            }
        }

        # 如果有args参数，添加到cmd中
        if "args" in extend_config:
            payload["cmd"]["args"] = extend_config["args"]

        try:
            response = requests.post(url, json=payload, timeout=self.timeout)
            response.raise_for_status()

            # 解析响应
            try:
                data = response.json()
            except json.JSONDecodeError:
                # 如果不是JSON，返回原始文本（去掉引号）
                text = response.text.strip()
                if text.startswith('"') and text.endswith('"'):
                    data = text[1:-1]  # 去掉外层引号
                else:
                    data = text

            # 提取指定字段
            field_value = self.extract_nested_field(data, extend_config["care"])

            if field_value is not None:
                return str(field_value)
            else:
                return "-"

        except requests.RequestException:
            return "ERROR"

    def extract_nested_field(self, data, field_path: str):
        """
        提取嵌套字段值（基于验证的实现）

        Args:
            data: 原始数据（JSON对象或字符串）
            field_path: 字段路径，支持点分隔符（如"version.major"）

        Returns:
            Any: 字段值，不存在返回None
        """
        if data is None:
            return None

        # 如果字段路径为空，直接返回原始数据
        if field_path == "":
            return data

        # 如果是字符串，尝试解析为JSON
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                # 如果不是JSON字符串，无法提取嵌套字段
                return None

        # 如果不是字典类型，无法提取嵌套字段
        if not isinstance(data, dict):
            return None

        # 分割字段路径
        fields = field_path.split('.')
        current = data

        for field in fields:
            if isinstance(current, dict) and field in current:
                current = current[field]
            else:
                return None

        return current

    @staticmethod
    def validate_extend_config(extend_configs: List[Dict]) -> bool:
        """验证扩展列配置格式"""
        if not extend_configs:
            return True

        required_fields = ["subtype", "action", "care", "name"]
        for config in extend_configs:
            if not all(field in config for field in required_fields):
                return False

        return True


# ================================
# 网关重启分析器
# ================================

class GatewayAnalyzer:
    """网关重启检测分析器"""
    
    def __init__(self, gateway_restart_threshold: int = 1800):  # 默认30分钟
        self.gateway_restart_threshold = gateway_restart_threshold
    
    def check_gateway_restart(self, plugin_uptime: float, gateway_uptime: float) -> Optional[bool]:
        """
        检测网关是否重启
        
        判断标准：sys_uptime - plg_uptime <= 阈值时间（默认30分钟）
        正常情况：plg_uptime <= sys_uptime（插件在系统启动后才启动）
        
        Args:
            plugin_uptime: 插件当前运行时长（秒）
            gateway_uptime: 网关系统当前运行时长（秒）
            
        Returns:
            bool: True表示网关发生过重启，False表示网关未重启
            None: 数据异常（plg_uptime > sys_uptime）
        """
        # 验证数据合理性：正常情况下plg_uptime应该≤sys_uptime
        if plugin_uptime > gateway_uptime:
            # 数据异常：插件运行时间不可能超过系统运行时间
            print_info(f"⚠️  数据异常 - 插件uptime({plugin_uptime:.2f}s) > 系统uptime({gateway_uptime:.2f}s)")
            return None  # 返回None表示数据异常，无法判断
        
        # 计算时间差：sys_uptime - plg_uptime
        time_diff = gateway_uptime - plugin_uptime
        
        # 判断网关是否重启：当插件启动时间接近系统启动时间时认为同时重启
        return time_diff <= self.gateway_restart_threshold
    
    def analyze_device_gateway_status(self, device_sn: str, device_restart_events: List[RestartEvent], 
                                    gateway_client) -> Optional[bool]:
        """
        分析设备的网关重启状态（实时获取并比较plg_uptime和sys_uptime）
        
        Args:
            device_sn: 设备序列号
            device_restart_events: 设备的重启事件列表
            gateway_client: 网关客户端实例
            
        Returns:
            bool or None: True表示网关重启，False表示网关未重启，None表示无重启记录或获取失败
        """
        if not device_restart_events:
            return None  # 设备无重启记录
        
        # 只检查有真实重启的设备
        real_restarts = [event for event in device_restart_events if event.event_type == 'restart']
        if not real_restarts:
            return None  # 设备无真实重启记录
        
        try:
            # 实时获取插件和网关的uptime
            plugin_uptime = gateway_client.get_plugin_uptime(device_sn)
            gateway_uptime = gateway_client.get_gateway_uptime(device_sn)
            
            # 使用检测方法判断网关是否重启
            return self.check_gateway_restart(plugin_uptime, gateway_uptime)
            
        except Exception as e:
            # 网关接口获取失败，返回None表示无法判断
            print_info(f"⚠️  设备 {device_sn} 网关接口获取失败: {e}")
            return None


# ================================
# 重启检测器
# ================================

class RestartDetector:
    """重启检测核心算法"""
    
    def __init__(self, anomaly_tolerance: timedelta):
        self.anomaly_tolerance = anomaly_tolerance
    
    def detect_restarts(self, uptime_data: List[DataPoint]) -> List[RestartEvent]:
        """
        检测uptime数据中的重启事件
        
        Args:
            uptime_data: 按时间排序的uptime数据点列表
            
        Returns:
            重启事件列表
        """
        if len(uptime_data) < 2:
            return []
        
        events = []
        
        for i in range(1, len(uptime_data)):
            current_point = uptime_data[i]
            previous_point = uptime_data[i-1]
            
            # 检测uptime下降
            if current_point.value < previous_point.value:
                
                # 分析异常容忍窗口内的恢复模式
                window_end = current_point.timestamp + self.anomaly_tolerance
                recovery_pattern = self._analyze_recovery_pattern(
                    uptime_data, i, window_end
                )
                
                # 分类事件类型
                if recovery_pattern.is_anomaly:
                    events.append(RestartEvent(
                        timestamp=current_point.timestamp,
                        event_type='anomaly',
                        uptime_before=previous_point.value,
                        uptime_after=current_point.value,
                        recovery_uptime=recovery_pattern.final_uptime
                    ))
                else:
                    events.append(RestartEvent(
                        timestamp=current_point.timestamp,
                        event_type='restart',
                        uptime_before=previous_point.value,
                        uptime_after=current_point.value
                    ))
        
        return events
    
    def _analyze_recovery_pattern(self, data: List[DataPoint], drop_index: int, window_end: datetime) -> RecoveryPattern:
        """
        分析异常容忍窗口内的uptime恢复模式
        
        Args:
            data: 完整的时序数据
            drop_index: 下降点索引
            window_end: 容忍窗口结束时间
            
        Returns:
            恢复模式分析结果
        """
        window_data = [p for p in data[drop_index:] if p.timestamp <= window_end]
        
        if len(window_data) < 2:
            return RecoveryPattern(
                is_restart=False,
                is_anomaly=True,  # 数据不足，视为异常
                final_uptime=window_data[0].value if window_data else 0.0
            )
        
        # 计算基础指标
        uptime_before = data[drop_index-1].value
        uptime_after_drop = window_data[0].value
        final_uptime = window_data[-1].value
        
        # 1. 计算恢复程度比例
        recovery_ratio = final_uptime / uptime_before if uptime_before > 0 else 0
        
        # 2. 计算增长趋势斜率 (uptime增长秒数 / 实际经过秒数)
        time_span = (window_data[-1].timestamp - window_data[0].timestamp).total_seconds()
        uptime_growth = final_uptime - uptime_after_drop
        growth_rate = uptime_growth / time_span if time_span > 0 else 0
        
        # 3. 综合判断逻辑
        # 真实重启特征判断（正向判断，更可靠）：
        # 1. 显著下降：uptime下降幅度大（通常>80%）
        # 2. 低起点恢复：重启后从很低的值开始
        # 3. 正常增长：增长率范围0.5-1.5（每秒增长约1秒，符合uptime定义）
        # 4. 持续恢复：没有快速恢复到原水平
        
        drop_percentage = (uptime_before - uptime_after_drop) / uptime_before if uptime_before > 0 else 0
        
        is_significant_drop = drop_percentage > 0.8        # 下降超过80%
        is_low_start = uptime_after_drop < 1000           # 重启后值很低（<1000秒）
        
        # 简化判断逻辑，按照需求文档设计：
        # - uptime恢复到下降前水平或更高 → 数据异常
        # - uptime持续低值并正常增长 → 确认重启
        
        is_normal_growth = 0.5 <= growth_rate <= 1.5       # 正常增长率（0.5-1.5）
        is_low_level_growth = recovery_ratio < 0.5          # 保持在50%以下认为是持续低值
        
        # 按需求逻辑：显著下降 + 低起点 + 持续低值 + 正常增长 → 真实重启
        is_restart = is_significant_drop and is_low_start and is_low_level_growth and is_normal_growth
        is_anomaly = not is_restart
        
        return RecoveryPattern(
            is_restart=is_restart,
            is_anomaly=is_anomaly,
            final_uptime=final_uptime,
            recovery_ratio=recovery_ratio,
            growth_rate=growth_rate
        )
    
    def analyze_device_restarts(self, device: Device, uptime_data: List[DataPoint],
                               memory_data: Optional[List[DataPoint]] = None,
                               post_restart_range_minutes: int = 5) -> DeviceResult:
        """
        分析单个设备的重启情况

        Args:
            device: 设备信息对象
            uptime_data: 设备的uptime时序数据
            memory_data: 设备的内存时序数据（可选）
            post_restart_range_minutes: 重启后内存检测范围（分钟）

        Returns:
            设备分析结果
        """
        # 检测重启事件
        events = self.detect_restarts(uptime_data)

        # 统计分类
        restart_events = [e for e in events if e.event_type == 'restart']
        anomaly_events = [e for e in events if e.event_type == 'anomaly']

        # 找到最后一次重启时间
        last_restart_time = None
        if restart_events:
            last_restart_time = max(restart_events, key=lambda x: x.timestamp).timestamp

        # 分析重启前后内存统计
        memory_stats = DeviceMemoryStats(
            before_restart=MemoryStats(None, None, None, 0),
            after_restart=MemoryStats(None, None, None, 0)
        )
        if memory_data:
            memory_analyzer = MemoryAnalyzer(post_restart_range_minutes)
            memory_stats = memory_analyzer.analyze_device_memory(memory_data, events)

        return DeviceResult(
            device=device,
            restart_count=len(restart_events),
            anomaly_count=len(anomaly_events),
            last_restart_time=last_restart_time,
            events=events,
            memory_stats=memory_stats
        )


# ================================
# 内存分析器
# ================================

class MemoryAnalyzer:
    """内存数据分析模块"""

    def __init__(self, post_restart_range_minutes: int = 5):
        self.post_restart_range_minutes = post_restart_range_minutes

    def get_memory_before_restart(self, memory_data: List[DataPoint],
                                restart_time: datetime,
                                lookback_minutes: int = 30) -> Optional[float]:
        """获取重启前的内存最大值，用于分析内存使用情况，默认30分钟内"""
        lookback_window = restart_time - timedelta(minutes=lookback_minutes)

        # 查找重启前时间窗口内的内存最大值
        memory_candidates = [
            point.value for point in memory_data
            if lookback_window <= point.timestamp < restart_time and point.value > 0
        ]
        memory_before = max(memory_candidates) if memory_candidates else None

        return memory_before

    def get_memory_after_restart(self, memory_data: List[DataPoint],
                               restart_time: datetime,
                               range_minutes: int) -> Optional[float]:
        """获取重启后指定时间范围内的最大内存值"""
        range_end = restart_time + timedelta(minutes=range_minutes)

        # 查找重启后时间范围内的所有内存值
        memory_candidates = [
            point.value for point in memory_data
            if restart_time <= point.timestamp <= range_end and point.value > 0
        ]

        # 在检测范围内取最大值
        memory_after = max(memory_candidates) if memory_candidates else None

        return memory_after

    def analyze_memory_before_restarts(self, memory_data: List[DataPoint],
                                     restart_events: List[RestartEvent]) -> MemoryStats:
        """分析重启前的内存使用情况"""
        memory_values = []

        for event in restart_events:
            if event.event_type == 'restart':
                memory_before = self.get_memory_before_restart(memory_data, event.timestamp)
                if memory_before is not None:
                    memory_values.append(memory_before)

        if memory_values:
            return MemoryStats(
                min_memory=min(memory_values),
                max_memory=max(memory_values),
                avg_memory=sum(memory_values) / len(memory_values),
                memory_count=len(memory_values)
            )
        else:
            return MemoryStats(None, None, None, 0)

    def analyze_memory_after_restarts(self, memory_data: List[DataPoint],
                                    restart_events: List[RestartEvent]) -> MemoryStats:
        """分析重启后的内存使用情况"""
        memory_values = []

        for event in restart_events:
            if event.event_type == 'restart':
                memory_after = self.get_memory_after_restart(
                    memory_data, event.timestamp, self.post_restart_range_minutes
                )
                if memory_after is not None:
                    memory_values.append(memory_after)

        if memory_values:
            return MemoryStats(
                min_memory=min(memory_values),
                max_memory=max(memory_values),
                avg_memory=sum(memory_values) / len(memory_values),
                memory_count=len(memory_values)
            )
        else:
            return MemoryStats(None, None, None, 0)

    def analyze_device_memory(self, memory_data: List[DataPoint],
                            restart_events: List[RestartEvent]) -> DeviceMemoryStats:
        """分析设备的重启前后内存使用情况"""
        before_stats = self.analyze_memory_before_restarts(memory_data, restart_events)
        after_stats = self.analyze_memory_after_restarts(memory_data, restart_events)

        return DeviceMemoryStats(
            before_restart=before_stats,
            after_restart=after_stats
        )


# ================================
# 时间解析器
# ================================

class TimeParser:
    """时间解析和处理工具"""
    
    @staticmethod
    def parse_time(time_str: str) -> datetime:
        """解析时间字符串为datetime对象（返回naive datetime）"""
        now = pendulum.now()
        
        # 尝试解析绝对时间格式
        absolute_formats = [
            "%Y-%m-%d %H:%M:%S",  # 2025-01-30 10:00:00
            "%Y-%m-%d %H:%M",     # 2025-01-30 10:00
            "%Y-%m-%d",           # 2025-01-30
        ]
        
        for fmt in absolute_formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                # 转换为pendulum对象进行比较
                aware_dt = pendulum.instance(dt).in_timezone(pendulum.local_timezone())
                if aware_dt >= now:
                    raise ParameterError(f"时间 '{time_str}' 不能大于等于当前时间")
                # 返回naive datetime
                return dt
            except ValueError:
                continue
        
        # 解析相对时间格式
        if re.match(r'^\d+[smhd]$', time_str):
            number = int(time_str[:-1])
            unit = time_str[-1]
            
            if unit == 's':
                delta = pendulum.duration(seconds=number)
            elif unit == 'm':
                delta = pendulum.duration(minutes=number)
            elif unit == 'h':
                delta = pendulum.duration(hours=number)
            elif unit == 'd':
                delta = pendulum.duration(days=number)
            else:
                raise ParameterError(f"不支持的时间单位: {unit}")
            
            # 返回naive datetime
            result_time = now - delta
            return datetime(result_time.year, result_time.month, result_time.day,
                           result_time.hour, result_time.minute, result_time.second)
        
        # 解析关键词
        if time_str == 'now':
            return datetime(now.year, now.month, now.day, now.hour, now.minute, now.second)
        elif time_str == 'today':
            start_of_day = now.start_of('day')
            return datetime(start_of_day.year, start_of_day.month, start_of_day.day)
        elif time_str == 'yesterday':
            yesterday = now.subtract(days=1).start_of('day')
            return datetime(yesterday.year, yesterday.month, yesterday.day)
        
        raise ParameterError(f"无法解析时间格式: {time_str}")
    
    @staticmethod
    def parse_duration(duration_str: str) -> timedelta:
        """解析时间间隔字符串"""
        if not re.match(r'^\d+[smhd]$', duration_str):
            raise ParameterError(f"无效的时间间隔格式: {duration_str}")
        
        number = int(duration_str[:-1])
        unit = duration_str[-1]
        
        if unit == 's':
            return timedelta(seconds=number)
        elif unit == 'm':
            return timedelta(minutes=number)
        elif unit == 'h':
            return timedelta(hours=number)
        elif unit == 'd':
            return timedelta(days=number)
        
        raise ParameterError(f"不支持的时间单位: {unit}")
    
    @staticmethod
    def parse_time_range_with_base_time(time_str: str, base_time_str: Optional[str] = None) -> Tuple[datetime, datetime]:
        """
        解析时间范围，支持基准时间功能

        Args:
            time_str: 时间参数（相对时间或绝对时间）
            base_time_str: 基准时间（可选，仅对相对时间有效）

        Returns:
            Tuple[start_time, end_time]: 计算出的时间范围
        """
        current_time = pendulum.now()

        # 判断是否为相对时间格式
        if re.match(r'^\d+[smhd]$', time_str):
            # 相对时间处理
            number = int(time_str[:-1])
            unit = time_str[-1]

            if unit == 's':
                delta = pendulum.duration(seconds=number)
            elif unit == 'm':
                delta = pendulum.duration(minutes=number)
            elif unit == 'h':
                delta = pendulum.duration(hours=number)
            elif unit == 'd':
                delta = pendulum.duration(days=number)
            else:
                raise ParameterError(f"不支持的时间单位: {unit}")

            if base_time_str:
                # 使用基准时间
                base_time = TimeParser.parse_base_time(base_time_str)
                end_time_aware = pendulum.instance(base_time).in_timezone(pendulum.local_timezone())
                if end_time_aware >= current_time:
                    raise ParameterError(f"基准时间 '{base_time_str}' 不能大于等于当前时间")

                end_time = base_time
                start_time_aware = end_time_aware - delta
                start_time = datetime(start_time_aware.year, start_time_aware.month, start_time_aware.day,
                                    start_time_aware.hour, start_time_aware.minute, start_time_aware.second)
            else:
                # 使用当前时间（默认行为）
                end_time = datetime(current_time.year, current_time.month, current_time.day,
                                  current_time.hour, current_time.minute, current_time.second)
                start_time_aware = current_time - delta
                start_time = datetime(start_time_aware.year, start_time_aware.month, start_time_aware.day,
                                    start_time_aware.hour, start_time_aware.minute, start_time_aware.second)
        else:
            # 绝对时间处理（忽略base_time_str）
            start_time = TimeParser.parse_time(time_str)
            end_time = datetime(current_time.year, current_time.month, current_time.day,
                              current_time.hour, current_time.minute, current_time.second)

            if base_time_str:
                # 给出友好提示：绝对时间模式下基准时间被忽略
                print("ℹ️  提示：使用绝对时间时，--base-time参数被忽略")

        # 验证时间范围合理性
        if start_time >= end_time:
            raise ParameterError(f"开始时间({start_time})不能大于等于结束时间({end_time})")

        return start_time, end_time

    @staticmethod
    def parse_base_time(base_time_str: str) -> datetime:
        """解析基准时间字符串"""
        # 支持多种格式
        formats = [
            '%Y-%m-%d %H:%M:%S',  # 2025-01-30 15:00:00
            '%Y-%m-%d %H:%M',     # 2025-01-30 15:00
            '%Y-%m-%d'            # 2025-01-30 (默认00:00:00)
        ]

        for fmt in formats:
            try:
                return datetime.strptime(base_time_str, fmt)
            except ValueError:
                continue

        raise ParameterError(f"无效的基准时间格式: {base_time_str}")

    @staticmethod
    def validate_time_range(start_time: datetime, end_time: datetime) -> None:
        """验证时间范围有效性"""
        if start_time >= end_time:
            raise ParameterError("开始时间必须小于结束时间")

        duration = end_time - start_time
        if duration.total_seconds() < 300:  # 5分钟
            raise ParameterError("时间范围过短，建议至少5分钟")


# ================================
# 结果格式化器
# ================================

class ResultFormatter:
    """结果输出格式化"""

    def __init__(self, csv_output_path: Optional[str] = None, extend_configs: List[Dict] = None):
        self.csv_output_path = csv_output_path
        self.extend_configs = extend_configs or []

    def format_device_table(self, device_results: List[DeviceResult]) -> str:
        """格式化设备结果表格（含网关重启状态）- 固定宽度对齐"""
        if not device_results:
            return "未找到设备数据"

        # 按照需求文档规定的表头文字（基础列 + 扩展列）
        headers = [
            "设备名称",
            "SN",
            "真实重启",
            "数据异常",
            "最后重启时间(网关重启)",
            "重启前内存(最小/最大/平均)",
            "重启后内存(最小/最大/平均)"
        ]

        # 添加扩展列表头
        extend_headers = [config["name"] for config in self.extend_configs]
        headers.extend(extend_headers)

        # 收集所有数据用于计算列宽
        all_rows = []
        for device_result in device_results:
            restart_time_col = self._format_restart_time_with_gateway(device_result)
            row = [
                device_result.device.name,
                device_result.device.sn,
                str(device_result.restart_count),
                str(device_result.anomaly_count),
                restart_time_col,
                device_result.memory_stats.before_restart.format_stats(),
                device_result.memory_stats.after_restart.format_stats()
            ]

            # 添加扩展列数据
            if device_result.extend_data:
                row.extend(device_result.extend_data)
            else:
                # 如果没有扩展列数据，填充空值
                row.extend(["-"] * len(self.extend_configs))

            all_rows.append(row)

        # 计算每列的最大宽度（考虑中文字符宽度）
        def get_display_width(text):
            """计算文本显示宽度（中文字符算2个宽度）"""
            width = 0
            for char in text:
                if ord(char) > 127:  # 非ASCII字符（包括中文）
                    width += 2
                else:
                    width += 1
            return width

        col_widths = []
        for i in range(len(headers)):
            # 表头宽度
            max_width = get_display_width(headers[i])
            # 数据行宽度
            for row in all_rows:
                width = get_display_width(row[i])
                max_width = max(max_width, width)
            # 添加一个制表符的间距（8个空格）
            col_widths.append(max_width + 8)

        lines = []

        # 表头行 - 左对齐，固定宽度
        header_parts = []
        for i, header in enumerate(headers):
            if i == len(headers) - 1:  # 最后一列不需要填充
                header_parts.append(header)
            else:
                # 计算需要填充的空格数
                display_width = get_display_width(header)
                padding = col_widths[i] - display_width
                header_parts.append(header + " " * padding)

        header_line = "".join(header_parts)
        lines.append(header_line)

        # 数据行 - 左对齐，固定宽度
        for row in all_rows:
            row_parts = []
            for i, cell in enumerate(row):
                if i == len(row) - 1:  # 最后一列不需要填充
                    row_parts.append(cell)
                else:
                    # 计算需要填充的空格数
                    display_width = get_display_width(cell)
                    padding = col_widths[i] - display_width
                    row_parts.append(cell + " " * padding)

            data_line = "".join(row_parts)
            lines.append(data_line)

        return "\n".join(lines)
    
    def _format_restart_time_with_gateway(self, device_result: DeviceResult) -> str:
        """格式化最后重启时间(网关重启)列"""
        if device_result.restart_count == 0:
            return "-"
        
        # 格式化最后重启时间
        last_restart_str = device_result.last_restart_time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 添加网关重启标识
        if device_result.gateway_restarted is None:
            # 无网关检测数据（如网关连接失败）
            gateway_status = "(-)"
        elif device_result.gateway_restarted:
            gateway_status = "(是)"
        else:
            gateway_status = "(否)"
        
        return f"{last_restart_str}{gateway_status}"
    
    def format_summary(self, device_results: List[DeviceResult], start_time: datetime,
                      end_time: datetime, anomaly_tolerance: timedelta,
                      post_restart_range: timedelta, base_time_str: Optional[str] = None) -> str:
        """格式化新格式的总结信息（支持基准时间）"""
        # 计算新格式的统计信息
        stats = self._calculate_summary_stats(device_results)

        # 格式化时间范围（含基准时间信息）
        time_range = f"{start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}"
        if base_time_str:
            time_range += f" (基准时间: {base_time_str})"

        # 格式化异常容忍时间
        tolerance_minutes = int(anomaly_tolerance.total_seconds() / 60)
        tolerance_str = f"{tolerance_minutes}分钟"

        # 格式化重启后内存检测范围
        post_restart_minutes = int(post_restart_range.total_seconds() / 60)
        post_restart_str = f"{post_restart_minutes}分钟"

        return self._format_new_summary(stats, time_range, tolerance_str, post_restart_str)
    
    def _calculate_summary_stats(self, device_results: List[DeviceResult]) -> dict:
        """计算新格式的统计信息"""
        total_devices = len(device_results)
        
        # 统计插件重启
        devices_with_restarts = [d for d in device_results if d.restart_count > 0]
        restart_count = len(devices_with_restarts)
        restart_rate = round(restart_count / total_devices * 100) if total_devices > 0 else 0
        
        # 统计网关重启（仅在有插件重启的设备中统计）
        gateway_restart_count = sum(1 for d in devices_with_restarts 
                                  if d.gateway_restarted is True)
        gateway_restart_rate = (round(gateway_restart_count / restart_count * 100) 
                              if restart_count > 0 else 0)
        
        # 统计异常次数
        total_anomalies = sum(d.anomaly_count for d in device_results)
        
        return {
            'total_devices': total_devices,
            'restart_count': restart_count,
            'restart_rate': restart_rate,
            'gateway_restart_count': gateway_restart_count, 
            'gateway_restart_rate': gateway_restart_rate,
            'total_anomalies': total_anomalies
        }
    
    def _format_new_summary(self, stats: dict, time_range: str, anomaly_tolerance: str,
                           post_restart_range: str) -> str:
        """新格式的总结信息"""
        lines = []

        # 分隔线
        lines.append("─" * 68)
        lines.append(f"时间范围: {time_range}")
        lines.append(f"检查设备: {stats['total_devices']}台 (异常容忍: {anomaly_tolerance}, 重启后内存检测范围: {post_restart_range})")
        lines.append("")

        # 新格式的总结统计
        summary_line = (f"总结: {stats['restart_count']}/{stats['total_devices']}台设备"
                       f"插件发生真实重启 ({stats['restart_rate']}%)")

        if stats['restart_count'] > 0:
            summary_line += (f"，其中{stats['gateway_restart_count']}/{stats['restart_count']}"
                           f"台设备网关发生过重启({stats['gateway_restart_rate']}%)")

        lines.append(summary_line)
        lines.append(f"     检测到{stats['total_anomalies']}次插件uptime数据异常 (已过滤)")

        return "\n".join(lines)
    
    def format_complete_result(self, device_results: List[DeviceResult], start_time: datetime,
                             end_time: datetime, anomaly_tolerance: timedelta,
                             post_restart_range: timedelta, base_time_str: Optional[str] = None) -> FormattedResult:
        """格式化完整结果（支持基准时间）"""
        # 先显示表格
        table = self.format_device_table(device_results)

        # 再显示汇总
        summary = self.format_summary(device_results, start_time, end_time,
                                    anomaly_tolerance, post_restart_range, base_time_str)

        # 格式化时间范围信息
        time_range = f"{start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}"

        # CSV导出（如果指定了输出路径）
        csv_file_path = None
        if self.csv_output_path:
            csv_file_path = self._export_to_csv(device_results)

        return FormattedResult(
            device_table=table,
            summary_info=summary,
            time_range=time_range,
            csv_file_path=csv_file_path
        )
    
    def _export_to_csv(self, device_results: List[DeviceResult]) -> str:
        """导出结果到CSV文件"""
        import csv
        from pathlib import Path

        # 确保目录存在
        csv_path = Path(self.csv_output_path)
        csv_path.parent.mkdir(parents=True, exist_ok=True)

        # 写入CSV文件
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # 构建CSV表头（基础列 + 扩展列）
            base_headers = ['设备名称', 'SN', '真实重启', '数据异常',
                           '最后重启时间(网关重启)', '重启前内存(最小/最大/平均)', '重启后内存(最小/最大/平均)']
            extend_headers = [config["name"] for config in self.extend_configs]
            all_headers = base_headers + extend_headers

            # 写入表头
            writer.writerow(all_headers)

            # 写入数据行
            for device_result in device_results:
                # 格式化最后重启时间(网关重启)
                restart_time_with_gateway = (
                    self._format_restart_time_with_gateway(device_result)
                    if device_result.restart_count > 0 else "-"
                )

                # 格式化重启前后内存统计
                before_memory_stats = device_result.memory_stats.before_restart.format_stats()
                after_memory_stats = device_result.memory_stats.after_restart.format_stats()

                # 构建基础数据行
                row_data = [
                    device_result.device.name,
                    device_result.device.sn,
                    device_result.restart_count,
                    device_result.anomaly_count,
                    restart_time_with_gateway,
                    before_memory_stats,
                    after_memory_stats
                ]

                # 添加扩展列数据
                if device_result.extend_data:
                    row_data.extend(device_result.extend_data)
                else:
                    # 如果没有扩展列数据，填充空值
                    row_data.extend(["-"] * len(self.extend_configs))

                writer.writerow(row_data)

        return str(csv_path.absolute())


# ================================
# 参数解析器
# ================================

def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="设备重启检测工具 - 通过Prometheus数据智能分析设备插件重启情况",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 检查所有设备过去24小时的重启情况
  python3 check_restarts.py -p ".*"
  
  # 检查中兴设备过去1小时的重启情况
  python3 check_restarts.py -p "zhongxing.*" -t 1h
  
  # 检查从指定时间到现在的重启情况
  python3 check_restarts.py -p "device.*" -t "2025-01-30 10:00"

  # 指定基准时间，然后往前推算时间范围
  python3 check_restarts.py -p "device.*" -t 24h --base-time "2025-01-30 15:00"

  # 调整数据异常容忍窗口为10分钟
  python3 check_restarts.py -p "device.*" --anomaly-tolerance 10m

  # 配置重启后内存检测范围为20分钟
  python3 check_restarts.py -p "device.*" --post-restart-range 20m

  # 使用自定义网关地址和重启判断阈值
  python3 check_restarts.py -p "device.*" --gateway-url http://************:21000 --gateway-threshold 15m

  # 导出结果为CSV文件，路径为当前目录下的restart_report.csv
  python3 check_restarts.py -p "zhongxing.*" -t 24h --csv-output restart_report.csv

  # 使用扩展列功能获取设备自定义属性
  python3 check_restarts.py -p "device.*" -X '[{"subtype":"http","action":"read","care":"period","name":"HTTP周期"}]'

  # 多个扩展列组合使用
  python3 check_restarts.py -p "zhongxing.*" -t 24h \\
    --extend-columns '[
      {"subtype":"http","action":"read","care":"period","name":"HTTP周期"},
      {"subtype":"sys","action":"dpi_uptime","care":"","name":"插件运行时间"}
    ]' \\
    --csv-output restart_report.csv

注意事项:
  - pattern: 设备名称正则表达式，必需参数
  - time: 支持相对时间(1h,24h,7d)和绝对时间(2025-01-30 10:00)
  - base-time: 基准时间，仅对相对时间有效，绝对时间模式下会被忽略
  - 绝对时间和基准时间不能大于等于当前时间
  - anomaly-tolerance: 异常容忍窗口，用于区分真实重启和数据异常
  - gateway-url: 网关接口地址，用于获取网关sys_uptime进行重启关联检测
  - gateway-threshold: 网关重启判断阈值，系统uptime与插件uptime相差不超过此时间时认为网关重启
  - extend-columns: 扩展列配置，JSON数组格式，每个配置包含subtype、action、care、name四个字段
        """
    )
    
    # 必需参数
    parser.add_argument(
        '-p', '--pattern',
        type=str,
        required=True,
        help='设备名称正则表达式 (必需)'
    )
    
    # 可选参数
    parser.add_argument(
        '-t', '--time',
        type=str,
        default='24h',
        help='时间范围 (默认: 24h), 支持相对时间如"1h","7d"或绝对时间如"2025-01-30 10:00"'
    )

    parser.add_argument(
        '--base-time',
        type=str,
        help='基准时间 (可选), 仅对相对时间有效，如"2025-01-30 15:00"，用于替代当前时间作为相对时间的起点'
    )
    
    parser.add_argument(
        '--anomaly-tolerance',
        type=str,
        default='20m',
        help='数据异常容忍窗口 (默认: 20m), 用于区分真实重启和数据异常'
    )

    parser.add_argument(
        '--post-restart-range',
        type=str,
        default='5m',
        help='重启后内存检测范围 (默认: 5m), 在该范围内取内存的最大值'
    )

    parser.add_argument(
        '--url',
        type=str,
        default='http://192.168.0.25:9090',
        help='Prometheus服务器地址 (默认: http://192.168.0.25:9090)'
    )
    
    parser.add_argument(
        '--gateway-url',
        type=str,
        default='http://************:21000',
        help='网关接口地址 (默认: http://************:21000)'
    )
    
    parser.add_argument(
        '--gateway-threshold',
        type=str,
        default='30m',
        help='网关重启判断阈值 (默认: 30m), 系统uptime与插件uptime相差不超过此时间时认为网关重启'
    )
    
    parser.add_argument(
        '--csv-output',
        type=str,
        help='CSV文件导出路径 (可选), 指定后将导出结果表格为CSV文件'
    )

    parser.add_argument(
        '-X', '--extend-columns',
        type=str,
        help='扩展列配置 (可选), JSON数组字符串，格式: [{"subtype":"http","action":"read","care":"period","name":"HTTP周期"}]'
    )

    return parser


def validate_arguments(args) -> None:
    """验证命令行参数"""
    # 验证正则表达式
    try:
        re.compile(args.pattern)
    except re.error as e:
        raise ParameterError(f"无效的正则表达式 '{args.pattern}': {e}")
    
    # 验证URL格式
    if not args.url.startswith(('http://', 'https://')):
        raise ParameterError(f"无效的URL格式 '{args.url}', 必须以http://或https://开头")
    
    # 验证网关URL格式
    if not args.gateway_url.startswith(('http://', 'https://')):
        raise ParameterError(f"无效的网关URL格式 '{args.gateway_url}', 必须以http://或https://开头")
    
    # 验证异常容忍时间格式
    if not re.match(r'^\d+[smhd]$', args.anomaly_tolerance):
        raise ParameterError(f"无效的异常容忍时间格式 '{args.anomaly_tolerance}', 支持格式: 1s, 5m, 1h, 1d")

    # 验证重启后内存检测范围格式
    if not re.match(r'^\d+[smhd]$', args.post_restart_range):
        raise ParameterError(f"无效的重启后内存检测范围格式 '{args.post_restart_range}', 支持格式: 1s, 5m, 1h, 1d")

    # 验证网关重启判断阈值格式
    if not re.match(r'^\d+[smhd]$', args.gateway_threshold):
        raise ParameterError(f"无效的网关重启判断阈值格式 '{args.gateway_threshold}', 支持格式: 1s, 5m, 1h, 1d")

    # 验证扩展列配置格式
    if hasattr(args, 'extend_columns') and args.extend_columns:
        try:
            extend_configs = json.loads(args.extend_columns)
            if not isinstance(extend_configs, list):
                raise ParameterError("扩展列配置必须是JSON数组格式")

            # 验证每个配置项的必需字段
            required_fields = ["subtype", "action", "care", "name"]
            for i, config in enumerate(extend_configs):
                if not isinstance(config, dict):
                    raise ParameterError(f"扩展列配置项 {i+1} 必须是JSON对象")

                missing_fields = [field for field in required_fields if field not in config]
                if missing_fields:
                    raise ParameterError(f"扩展列配置项 {i+1} 缺少必需字段: {missing_fields}")

        except json.JSONDecodeError as e:
            raise ParameterError(f"扩展列配置JSON格式错误: {e}")

    # 验证基准时间格式（如果提供）
    if hasattr(args, 'base_time') and args.base_time:
        try:
            TimeParser.parse_base_time(args.base_time)
        except ParameterError as e:
            raise ParameterError(f"基准时间格式错误: {e}")


# ================================
# 主程序框架
# ================================

class CheckRestartsApp:
    """重启检测应用主控制器"""
    
    def __init__(self):
        self.prometheus_client = None
        self.gateway_client = None
        self.extend_column_client = None
        self.gateway_analyzer = None
        self.time_parser = None
        self.restart_detector = None
        self.result_formatter = None
        self.args = None
        self.extend_configs = []
    
    def parse_arguments(self) -> None:
        """解析命令行参数"""
        parser = create_argument_parser()
        self.args = parser.parse_args()
        
        # 验证参数
        validate_arguments(self.args)

        # 解析扩展列配置
        if hasattr(self.args, 'extend_columns') and self.args.extend_columns:
            self.extend_configs = json.loads(self.args.extend_columns)
            print_info(f"扩展列配置: {len(self.extend_configs)}个扩展列")

        base_time_info = f", base_time='{getattr(self.args, 'base_time', None)}'" if getattr(self.args, 'base_time', None) else ""
        print_info(f"参数解析完成: pattern='{self.args.pattern}', time='{self.args.time}'{base_time_info}, "
                  f"tolerance='{self.args.anomaly_tolerance}', post_restart_range='{self.args.post_restart_range}', "
                  f"url='{self.args.url}', gateway_url='{self.args.gateway_url}', gateway_threshold='{self.args.gateway_threshold}'")
    
    def initialize_clients(self) -> None:
        """初始化各模块客户端"""
        # P2-1 初始化Prometheus客户端
        self.prometheus_client = PrometheusClient(self.args.url)
        
        # 测试连接
        print_info("测试Prometheus连接...")
        if not self.prometheus_client.test_connection():
            raise PrometheusConnectionError(f"无法连接到Prometheus服务器: {self.args.url}")
        print_success("Prometheus连接成功")
        
        # P2-2 初始化时间解析器
        self.time_parser = TimeParser()
        
        # P2-4 初始化网关客户端
        self.gateway_client = GatewayClient(self.args.gateway_url)
        
        # 测试网关连接（可选，失败不阻断）
        print_info("测试网关连接...")
        try:
            if self.gateway_client.test_connection():
                print_success("网关连接成功")
            else:
                print_info("⚠️  警告：网关连接失败，跳过网关重启检测")
        except Exception as e:
            print_info(f"⚠️  警告：网关连接测试异常: {e}，跳过网关重启检测")
        
        # P3-5 初始化网关分析器
        gateway_threshold_seconds = self.time_parser.parse_duration(self.args.gateway_threshold).total_seconds()
        self.gateway_analyzer = GatewayAnalyzer(int(gateway_threshold_seconds))

        # 初始化扩展列客户端（如果有扩展列配置）
        if self.extend_configs:
            self.extend_column_client = ExtendColumnClient(self.args.gateway_url)
            print_info(f"扩展列功能已启用，配置了{len(self.extend_configs)}个扩展列")
    
    def run(self):
        """主程序入口"""
        try:
            print_info("重启检测脚本启动中...")
            
            # P1-2 参数解析
            self.parse_arguments()
            
            # P2-1 & P2-2 初始化客户端和时间解析器
            self.initialize_clients()
            
            # P2-2 解析时间范围（支持基准时间）
            base_time_str = getattr(self.args, 'base_time', None)
            start_time, end_time = self.time_parser.parse_time_range_with_base_time(self.args.time, base_time_str)
            anomaly_tolerance = self.time_parser.parse_duration(self.args.anomaly_tolerance)
            post_restart_range = self.time_parser.parse_duration(self.args.post_restart_range)

            self.time_parser.validate_time_range(start_time, end_time)

            # 显示时间范围信息（含基准时间）
            if base_time_str and re.match(r'^\d+[smhd]$', self.args.time):
                print_info(f"时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')} (基准时间: {base_time_str})")
            else:
                print_info(f"时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print_info(f"重启后内存检测范围: {int(post_restart_range.total_seconds() / 60)}分钟")
            
            # P2-3 获取设备列表
            print_info(f"查找匹配设备: {self.args.pattern}")
            devices = self.prometheus_client.get_devices(self.args.pattern)
            if not devices:
                print("⚠️  未找到匹配的设备")
                return
            
            print_success(f"找到 {len(devices)} 台匹配设备")
            for device in devices[:5]:  # 显示前5台设备
                print_info(f"  - {device.name} ({device.sn})")
            if len(devices) > 5:
                print_info(f"  ... 共 {len(devices)} 台设备")
            
            # P2-3 获取uptime和内存数据
            print_info("获取uptime时序数据...")
            uptime_data = self.prometheus_client.get_uptime_data(devices, start_time, end_time, self.args.pattern)

            print_info("获取内存时序数据...")
            memory_data = self.prometheus_client.get_memory_data(devices, start_time, end_time, self.args.pattern)

            data_summary = {}
            for device_name, datapoints in uptime_data.items():
                data_summary[device_name] = len(datapoints)

            print_success(f"获取uptime数据完成，共 {len(uptime_data)} 台设备有数据")
            print_success(f"获取内存数据完成，共 {len(memory_data)} 台设备有数据")
            for device_name, point_count in list(data_summary.items())[:3]:
                print_info(f"  - {device_name}: {point_count} 个uptime数据点")
            
            # P3-1 初始化重启检测器
            self.restart_detector = RestartDetector(anomaly_tolerance)
            
            # P3-1,P3-2,P3-3 执行重启检测
            print_info("开始分析重启事件...")
            device_results = []
            
            # 创建设备名到设备对象的映射
            device_map = {device.name: device for device in devices}
            
            # 计算重启后内存检测范围（分钟）
            post_restart_range_minutes = int(post_restart_range.total_seconds() / 60)

            for device_name, datapoints in uptime_data.items():
                device = device_map.get(device_name)
                if device:
                    # 获取对应的内存数据
                    device_memory_data = memory_data.get(device_name, [])
                    device_result = self.restart_detector.analyze_device_restarts(
                        device, datapoints, device_memory_data, post_restart_range_minutes
                    )
                    device_results.append(device_result)
            
            # 统计有真实重启事件的设备数（只统计restart事件，不含anomaly）
            real_restart_device_count = sum(1 for r in device_results if r.restart_count > 0)
            print_success(f"重启事件分析完成，共 {real_restart_device_count} 台设备有重启事件")
            
            # P3-5 网关重启检测（仅对有真实重启的设备）
            print_info("开始网关重启检测...")
            gateway_restart_count = 0
            gateway_error_count = 0
            
            for device_result in device_results:
                if device_result.restart_count > 0:  # 仅对有重启的设备进行检测
                    try:
                        # 分析网关重启状态（实时获取并比较plg_uptime和sys_uptime）
                        device_result.gateway_restarted = self.gateway_analyzer.analyze_device_gateway_status(
                            device_result.device.sn, device_result.events, self.gateway_client
                        )
                        
                        if device_result.gateway_restarted:
                            gateway_restart_count += 1
                            
                    except Exception as e:
                        print_info(f"⚠️  设备 {device_result.device.name} 网关检测失败: {e}")
                        device_result.gateway_restarted = None  # 标记为检测失败
                        gateway_error_count += 1
            
            if gateway_error_count == 0:
                print_success(f"网关重启检测完成，{gateway_restart_count} 台设备网关发生过重启")
            else:
                print_info(f"⚠️  网关重启检测完成，{gateway_restart_count} 台设备网关发生过重启，{gateway_error_count} 台设备检测失败")

            # 扩展列数据收集（如果启用了扩展列功能）
            if self.extend_configs and self.extend_column_client:
                print_info("开始收集扩展列数据...")
                extend_error_count = 0

                for device_result in device_results:
                    extend_values = []

                    # 按配置顺序获取每个扩展列的数据
                    for config in self.extend_configs:
                        try:
                            value = self.extend_column_client.get_extend_column_data(device_result.device.sn, config)
                            extend_values.append(value)
                        except Exception as e:
                            # 单个扩展列失败不影响其他列
                            print_info(f"⚠️  设备 {device_result.device.name} 扩展列 {config.get('name', 'Unknown')} 获取失败: {e}")
                            extend_values.append("ERROR")
                            extend_error_count += 1

                    device_result.extend_data = extend_values

                if extend_error_count == 0:
                    print_success(f"扩展列数据收集完成，{len(self.extend_configs)}个扩展列")
                else:
                    print_info(f"⚠️  扩展列数据收集完成，{extend_error_count} 个数据项获取失败")

            # P4-1 格式化并输出结果
            self.result_formatter = ResultFormatter(csv_output_path=self.args.csv_output, extend_configs=self.extend_configs)
            # 传递基准时间信息给结果格式化器
            base_time_str = getattr(self.args, 'base_time', None) if re.match(r'^\d+[smhd]$', self.args.time) else None
            formatted_result = self.result_formatter.format_complete_result(
                device_results, start_time, end_time, anomaly_tolerance, post_restart_range, base_time_str
            )
            
            # 清空之前的进度信息，显示最终结果
            print("\n" + "=" * 70)
            print("🔍 重启检测分析结果")
            print("=" * 70)
            print(f"{formatted_result.device_table}\n\n{formatted_result.summary_info}")
            
            # 如果导出了CSV文件，显示确认信息
            if formatted_result.csv_file_path:
                print_success(f"检测完成，结果已导出至: {formatted_result.csv_file_path}")
            else:
                print_success("检测完成")
            
        except KeyboardInterrupt:
            print("\n⚠️  用户中断操作")
            sys.exit(130)
        except CheckRestartsError as e:
            exit_with_error(str(e))
        except Exception as e:
            exit_with_error(f"未知错误: {e}", 4)


# ================================
# 程序入口
# ================================

def main():
    """程序主入口"""
    app = CheckRestartsApp()
    app.run()


if __name__ == "__main__":
    main()