## 📋 重启检测脚本 - 简化需求文档

### 🎯 目标
创建一个**简单、专注**的工具，快速检测设备重启情况

### 🏗️ 设计原则
- **简单**: 最少参数，最直观输出
- **专注**: 只做重启检测，不做其他  
- **快速**: 10秒内得到结果

### 📊 核心功能
1. 从Prometheus获取设备uptime数据
2. 从Prometheus获取设备进程实际物理内存占用数据
3. 检测uptime下降事件（任何下降都可能是重启）
4. 区分真实重启和数据异常：
   - **数据异常**：uptime短暂降为0后快速恢复到异常前水平或更高
   - **真实重启**：uptime降为0后逐步正常增长
5. 统计每台设备在检测时间段内每次重启前的实际物理内存占用
6. 计算并显示每台设备重启前内存占用的最小值、最大值和平均值
7. **新增**：统计每台设备在检测时间段内每次重启后的实际物理内存占用
8. **新增**：支持配置重启后内存检测范围（如5m、10m、20m、30m等），在该范围内取内存的最大值
9. **新增**：计算并显示每台设备重启后内存占用的最小值、最大值和平均值
10. **网关重启检测**：对于插件发生真实重启的设备，检测网关是否同时重启：
   - 从接口(192.168.0.27:21000)实时获取网关本身的sys_uptime和插件本身的plg_uptime
   - 比较这两个实时数据：plg_uptime与sys_uptime的差值
   - 如果时间相差不超过30分钟，认为网关发生过重启
11. 统计并显示确认的真实重启结果、重启前后内存统计和网关重启情况
12. **新增**：支持将统计结果表格导出为CSV文件，便于后续数据分析和存档
13. **新增**：支持动态扩展表格列功能，通过网关接口获取设备自定义属性信息并显示在结果表格中
14. **新增**：支持基准时间功能，允许指定一个时间点作为"当前时间"，然后从该时间点往前推算时间范围，便于历史时间段分析

### 💻 用户接口

**脚本名称**: `check_restarts.py`

**基本用法**:
```bash
# 检查所有设备过去24小时
python3 check_restarts.py -p ".*"

# 检查特定设备过去1小时  
python3 check_restarts.py -p "zhongxing.*" -t 1h

# 检查从指定时间到现在的重启情况
python3 check_restarts.py -p "device.*" -t "2025-01-30 10:00"

# 指定基准时间，然后往前推算时间范围
python3 check_restarts.py -p "device.*" -t 24h --base-time "2025-01-30 15:00"

# 从指定基准时间往前推1小时的重启情况
python3 check_restarts.py -p "zhongxing.*" -t 1h --base-time "2025-01-30 14:30"

# 调整数据异常容忍窗口
python3 check_restarts.py -p "device.*" --anomaly-tolerance 10m

# 配置重启后内存检测范围为20分钟
python3 check_restarts.py -p "device.*" --post-restart-range 20m

# 导出结果为CSV文件
python3 check_restarts.py -p "zhongxing.*" -t 24h --csv-output restart_report.csv

# 使用扩展列功能获取设备自定义属性
python3 check_restarts.py -p "device.*" -X '[{"subtype":"http","action":"read","care":"period","name":"HTTP周期"}]'

# 多个扩展列组合使用
python3 check_restarts.py -p "zhongxing.*" -t 24h \
  --extend-columns '[
    {"subtype":"http","action":"read","care":"period","name":"HTTP周期"},
    {"subtype":"shell","action":"read","care":"version.major","name":"主版本"},
    {"subtype":"dpi","action":"status","care":"memory.usage","name":"DPI内存"}
  ]' \
  --csv-output restart_report.csv

# 基于指定基准时间的历史分析
python3 check_restarts.py -p "device.*" -t 6h --base-time "2025-01-29 18:00" \
  --csv-output historical_analysis.csv
```

**参数设计**（9个参数）:
| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `-p, --pattern` | 设备名称正则（**必需**） | 无 | `"zhongxing.*"` |
| `-t, --time` | 时间范围 | `24h` | `1h`, `6h`, `24h`, `7d`, `"2025-01-30 10:00"` |
| `--base-time` | 基准时间（可选） | 无（使用当前时间） | `"2025-01-30 15:00"`, `"2025-01-29 18:30:45"` |
| `--anomaly-tolerance` | 数据异常容忍窗口 | `5m` | `3m`, `10m` |
| `--post-restart-range` | 重启后内存检测范围 | `5m` | `5m`, `10m`, `20m`, `30m` |
| `--url` | Prometheus地址 | `http://192.168.0.25:9090` | 自定义URL |
| `--gateway-url` | 网关接口地址 | `http://192.168.0.27:21000` | 网关sys_uptime接口 |
| `--csv-output` | CSV导出文件路径（可选） | 无 | `restart_report.csv`, `./reports/data.csv` |
| `-X, --extend-columns` | 扩展列配置（可选） | 无 | JSON数组字符串，详见扩展列功能 |


### 📤 输出格式（统一简单）

**基础输出格式**：
```
设备名称                SN            真实重启  数据异常   最后重启时间(网关重启)      重启前内存(最小/最大/平均) 重启后内存(最小/最大/平均)
device001              389E80BB4EE9  2        1        2025-01-31 08:30:15(是)  32.5MB/45.8MB/39.2MB   28.1MB/35.2MB/31.7MB
device002              F2A1B3C4D5E6  0        0        -                        -                      -
device003              A7B8C9D0E1F2  1        2        2025-01-30 15:45:30(否)  28.3MB/28.3MB/28.3MB   25.8MB/25.8MB/25.8MB
```

**带扩展列的输出格式**：
```
设备名称                SN            真实重启  数据异常   最后重启时间(网关重启)      重启前内存(最小/最大/平均) 重启后内存(最小/最大/平均) HTTP周期  主版本  DPI内存
device001              389E80BB4EE9  2        1        2025-01-31 08:30:15(是)  32.5MB/45.8MB/39.2MB   28.1MB/35.2MB/31.7MB   30       2.1    47185920
device002              F2A1B3C4D5E6  0        0        -                        -                      -                      ERROR    2.0    -
device003              A7B8C9D0E1F2  1        2        2025-01-30 15:45:30(否)  28.3MB/28.3MB/28.3MB   25.8MB/25.8MB/25.8MB   15       1.8    35428864

────────────────────────────────────────────────────────────────
时间范围: 2025-01-30 10:00:00 - 2025-01-31 10:00:00
检查设备: 5台 (异常容忍: 5分钟, 重启后内存检测范围: 5分钟)

总结: 2/5台设备插件发生真实重启 (40%)，其中1/2台设备网关发生过重启(50%)
     检测到3次插件uptime数据异常 (已过滤)
```

#### 📝 输出格式调整与检测详情字段说明

- **输出顺序调整**：将"时间范围"、"设备数量"、"异常容忍"等汇总信息放在**输出末尾**，便于用户一眼看到检测结果后再查阅统计信息。
- **检测详情表格**：在原有表格基础上，**增加一列 `SN`**（设备序列号或唯一标识），便于区分同名设备或后续追溯。
- **网关重启标识列**：修改"最后重启时间"列为"最后重启时间(网关重启)"：
  - **格式**：`2025-01-31 08:30:15(是)` 或 `2025-01-30 15:45:30(否)`
  - **网关重启判断**：仅对插件发生真实重启的设备进行检测
     - **判断标准**：网关sys_uptime与插件plg_uptime相差不超过30分钟为"是"，否则为"否"
  - **无重启设备**：显示"-"表示无重启记录
- **内存统计列**：新增"重启前内存(最小/最大/平均)"列，显示每台设备在检测时间段内所有重启前的实际物理内存占用统计：
  - **最小值**：所有重启前实际物理内存占用的最小值
  - **最大值**：所有重启前实际物理内存占用的最大值
  - **平均值**：所有重启前实际物理内存占用的平均值
  - **格式**：内存单位显示，自动选择合适单位(KB/MB)，保留1位小数，如"32.5MB/45.8MB/39.2MB"
  - **无重启设备**：显示"-"表示无重启记录
- **重启后内存统计列**：新增"重启后内存(最小/最大/平均)"列，显示每台设备在检测时间段内所有重启后的实际物理内存占用统计：
  - **检测范围**：通过`--post-restart-range`参数配置，默认为5分钟，支持5m、10m、20m、30m等
  - **数据采集**：在重启后的指定时间范围内采集内存数据点
  - **最大值优先**：在检测范围内取内存的最大值作为该次重启后的内存占用值
  - **统计计算**：基于所有重启后内存最大值，计算最小值、最大值和平均值
  - **格式**：内存单位显示，自动选择合适单位(KB/MB)，保留1位小数，如"28.1MB/35.2MB/31.7MB"
  - **无重启设备**：显示"-"表示无重启记录
- **扩展列**：通过`-X`或`--extend-columns`参数配置的自定义属性列：
  - **数据来源**：通过网关接口`http://192.168.0.27:21000/api/v1/diagnose/do`获取设备自定义属性
  - **列头显示**：使用配置中的`name`字段作为列头名称
  - **数据显示**：显示JSON响应中的原始数据内容，不进行数值格式化
  - **错误处理**：网关连接失败显示`"ERROR"`，字段不存在显示`"-"`
  - **列顺序**：按照配置数组中的顺序从左到右显示

### 📊 CSV导出格式

当指定 `--csv-output` 参数时，将同时生成CSV文件，包含与控制台输出相同的数据：

**基础CSV文件格式**：
```csv
设备名称,SN,真实重启,数据异常,最后重启时间(网关重启),重启前内存(最小/最大/平均),重启后内存(最小/最大/平均)
device001,389E80BB4EE9,2,1,2025-01-31 08:30:15(是),32.5MB/45.8MB/39.2MB,28.1MB/35.2MB/31.7MB
device002,F2A1B3C4D5E6,0,0,-,-,-
device003,A7B8C9D0E1F2,1,2,2025-01-30 15:45:30(否),28.3MB/28.3MB/28.3MB,25.8MB/25.8MB/25.8MB
```

**带扩展列的CSV文件格式**：
```csv
设备名称,SN,真实重启,数据异常,最后重启时间(网关重启),重启前内存(最小/最大/平均),重启后内存(最小/最大/平均),HTTP周期,主版本,DPI内存
device001,389E80BB4EE9,2,1,2025-01-31 08:30:15(是),32.5MB/45.8MB/39.2MB,28.1MB/35.2MB/31.7MB,30,2.1,47185920
device002,F2A1B3C4D5E6,0,0,-,-,-,ERROR,2.0,-
device003,A7B8C9D0E1F2,1,2,2025-01-30 15:45:30(否),28.3MB/28.3MB/28.3MB,25.8MB/25.8MB/25.8MB,15,1.8,35428864
```

**CSV导出特性**：
- 使用UTF-8编码，支持中文字符
- 使用逗号分隔符，兼容Excel和其他数据分析工具
- 包含完整的表头和数据行（包括所有扩展列）
- 文件路径支持相对路径和绝对路径
- 自动创建目录（如果不存在）
- 导出成功后显示文件路径确认
- 扩展列按配置顺序添加到CSV文件中

### 🕐 基准时间功能详细规格

#### 📋 功能概述
基准时间功能允许用户指定一个历史时间点作为"当前时间"，然后使用相对时间参数从该基准时间往前推算时间范围。这对于分析历史特定时间段的设备重启情况非常有用。

#### 🎯 使用场景
- **历史故障分析**：分析某个已知故障时间点前的设备重启情况
- **定期报告生成**：生成特定历史时间段的重启统计报告
- **对比分析**：对比不同历史时间段的重启模式
- **事件回溯**：基于事件发生时间回溯分析设备状态

#### 🔧 参数配置
**参数名称**: `--base-time`
**参数类型**: 可选参数，字符串格式
**默认行为**: 未指定时使用当前系统时间作为基准

**支持格式**:
```bash
# 完整日期时间格式
--base-time "2025-01-30 15:00:00"

# 简化格式（秒数默认为00）
--base-time "2025-01-30 15:00"

# 日期格式（时间默认为00:00:00）
--base-time "2025-01-30"
```

#### 🚀 工作模式

**模式1：相对时间 + 基准时间**
```bash
# 从2025-01-30 15:00往前推24小时（即2025-01-29 15:00 到 2025-01-30 15:00）
python3 check_restarts.py -p "device.*" -t 24h --base-time "2025-01-30 15:00"

# 从2025-01-29 18:30往前推6小时（即2025-01-29 12:30 到 2025-01-29 18:30）
python3 check_restarts.py -p "zhongxing.*" -t 6h --base-time "2025-01-29 18:30"
```

**模式2：绝对时间（忽略基准时间）**
```bash
# 从2025-01-30 10:00到当前时间，--base-time参数被忽略
python3 check_restarts.py -p "device.*" -t "2025-01-30 10:00" --base-time "2025-01-30 15:00"
```

**模式3：仅相对时间（默认行为）**
```bash
# 从当前时间往前推24小时，等同于未指定--base-time
python3 check_restarts.py -p "device.*" -t 24h
```

#### ⚠️ 验证规则
1. **时间格式验证**：基准时间必须符合支持的日期时间格式
2. **时间范围验证**：基准时间不能大于等于当前系统时间
3. **逻辑一致性验证**：计算出的开始时间不能大于结束时间
4. **数据可用性提示**：如果指定的历史时间范围超出Prometheus数据保留期，给出友好提示

#### 💡 使用示例
```bash
# 分析2025年1月30日下午3点前24小时的重启情况
python3 check_restarts.py -p "zhongxing.*" -t 24h --base-time "2025-01-30 15:00"

# 生成2025年1月29日全天的重启报告
python3 check_restarts.py -p "device.*" -t 24h --base-time "2025-01-30 00:00" \
  --csv-output "restart_report_20250129.csv"

# 对比分析：先分析故障前6小时
python3 check_restarts.py -p "critical.*" -t 6h --base-time "2025-01-29 14:00"
# 再分析故障后6小时
python3 check_restarts.py -p "critical.*" -t 6h --base-time "2025-01-29 20:00"
```

#### 🔄 输出格式调整
当使用基准时间功能时，输出中的"时间范围"信息会明确显示计算出的实际时间范围：

```
────────────────────────────────────────────────────────────────
时间范围: 2025-01-29 15:00:00 - 2025-01-30 15:00:00 (基准时间: 2025-01-30 15:00:00)
检查设备: 5台 (异常容忍: 5分钟, 重启后内存检测范围: 5分钟)

总结: 2/5台设备插件发生真实重启 (40%)，其中1/2台设备网关发生过重启(50%)
     检测到3次插件uptime数据异常 (已过滤)
```

### 🔧 扩展列功能详细规格

#### 📋 功能概述
扩展列功能允许用户通过网关接口动态获取设备的自定义属性信息，并将这些信息作为额外的列显示在结果表格中。

#### 🎯 参数配置
**参数名称**: `-X` 或 `--extend-columns`
**参数类型**: JSON数组字符串（可选参数）
**配置格式**: 每个扩展列配置包含4个必需字段：

```json
[
  {
    "subtype": "http",           // 网关查询子类型
    "action": "read",            // 网关查询动作
    "care": "period",            // 关注的JSON字段路径
    "name": "HTTP周期"           // 扩展列的表头显示名称
  }
]
```

**字段说明**：
- **subtype**: 网关查询子类型，如 `"http"`, `"shell"`, `"dpi"` 等
- **action**: 网关查询动作，如 `"read"`, `"status"`, `"get"` 等
- **care**: 关注的JSON字段路径，支持嵌套字段（使用`.`分隔符），如 `"period"`, `"version.major"`, `"memory.usage"`
- **name**: 扩展列的表头显示名称，支持中文

#### 🌐 网关接口交互
**接口地址**: `http://192.168.0.27:21000/api/v1/diagnose/do`
**请求方法**: POST
**请求格式**:
```json
{
  "sn": "设备SN号",
  "cmd": {
    "subtype": "http",
    "action": "read"
  }
}
```

**响应处理**:
- 解析返回的JSON响应
- 根据`care`字段路径提取数据
- 支持嵌套字段访问，如`"version.major"`对应`response["version"]["major"]`

#### 📊 数据处理逻辑
1. **顺序请求**: 同一设备的多个扩展列按配置数组顺序依次请求
2. **独立请求**: 每个subtype/action组合需要单独发送请求
3. **错误处理**:
   - 网关连接失败或超时: 显示 `"ERROR"`
   - JSON字段不存在或路径无效: 显示 `"-"`
   - 单个扩展列失败不影响其他列和主要功能
4. **数据显示**: 显示JSON响应中的原始数据内容，**不进行数值格式化**
5. **超时设置**: 每个网关请求设置合理的超时时间（建议5-10秒）

#### 💡 使用示例
```bash
# 单个扩展列
python3 check_restarts.py -p "device.*" \
  -X '[{"subtype":"http","action":"read","care":"period","name":"HTTP周期"}]'

# 多个扩展列
python3 check_restarts.py -p "zhongxing.*" \
  --extend-columns '[
    {"subtype":"http","action":"read","care":"period","name":"HTTP周期"},
    {"subtype":"shell","action":"read","care":"version.major","name":"主版本"},
    {"subtype":"dpi","action":"status","care":"memory.usage","name":"DPI内存"}
  ]'

# 嵌套字段示例
python3 check_restarts.py -p "device.*" \
  -X '[{"subtype":"system","action":"info","care":"cpu.cores","name":"CPU核心数"}]'
```

#### ⚠️ 技术约束
- **JSON验证**: 启动时验证JSON格式和必需字段
- **字段路径**: 支持最多3层嵌套（如`a.b.c`）
- **列数限制**: 建议扩展列数量不超过5个，避免表格过宽
- **性能影响**: 每个扩展列会增加网关请求，影响总执行时间
- **网络依赖**: 扩展列功能依赖网关接口可用性

### 🚫 移除的复杂功能
- ❌ 复杂时间解析（绝对时间、yesterday等）
- ❌ 多种输出格式（JSON/simple选择）
- ❌ 调试模式的详细打印
- ❌ 文件输出功能
- ❌ 详细timeline和数据结构
- ❌ 内置测试功能
- ❌ 可读时间格式转换

### 🎯 技术约束
- **简单高效实现**：可使用第三方库，优先选择简洁高效的解决方案
- **依赖管理**：提供 `requirements.txt` 依赖说明文件
- **单文件实现**
- **代码行数 < 360行**（包含网关重启检测功能、扩展列功能和基准时间功能）
- **运行时间 < 15秒**（考虑扩展列网关请求时间）

### 📦 推荐技术栈
- **HTTP请求**: `requests` (比urllib更简洁)
- **时间处理**: `pendulum` 或 `arrow` (比datetime更直观)
- **参数解析**: `argparse` (标准库，功能够用)
- **JSON处理**: `json` (标准库，无需替换)

### 🔧 实现重点
1. **增强时间处理**: 支持相对时间、具体时间和基准时间三种模式
   - **相对时间**: `1h`, `6h`, `24h`, `7d` (从当前时间或基准时间向前推算)
   - **具体时间**: `"2025-01-30 10:00"`, `"2025-01-30 10:30:45"` (从指定时间到现在，忽略基准时间)
   - **基准时间**: `--base-time "2025-01-30 15:00"` (替代当前时间作为相对时间的起点)
   - **时间验证**: 具体时间和基准时间不能大于等于当前时间
2. **智能重启检测**: 区分真实重启和数据异常
3. **简化输出**: 固定表格格式，显示真实重启和异常统计
4. **简化错误处理**: 基本的try-catch
5. **去除调试**: 遇到问题直接报错退出
6. **扩展列功能**:
   - JSON配置验证和解析
   - 网关接口客户端扩展
   - 嵌套字段提取函数
   - 动态表格列渲染
   - 错误处理和容错机制

### 🧠 检测逻辑设计
**新的重启检测算法**：
1. **发现uptime下降** → 标记为"疑似重启事件"
2. **异常检查窗口**（`anomaly-tolerance`时间内）：
   - 如果uptime恢复到下降前水平或更高 → **数据异常**，记录但不计入重启
   - 如果uptime持续低值并正常增长 → **确认重启**
3. **内存数据收集**：
   - **重启前内存**：对于每个确认的重启事件，获取重启前最后一个有效的实际物理内存占用数据点
   - **重启后内存**：在重启后的指定时间范围内（`--post-restart-range`参数配置）采集内存数据点，取该范围内的最大值
   - 收集该设备在检测时间段内所有重启前和重启后的内存占用值
   - 分别计算重启前和重启后内存的最小值、最大值和平均值，并转换为合适的内存单位(KB/MB)
4. **网关重启检测**（仅对插件真实重启的设备）：
   - 从网关接口(192.168.0.27:21000)实时获取网关当前sys_uptime和插件当前plg_uptime
   - 验证数据合理性：正常情况下plg_uptime ≤ sys_uptime
   - 计算时间差值：sys_uptime - plg_uptime
   - **判断标准**：时间差 ≤ 30分钟 → 网关重启(是)，否则 → 网关重启(否)
   - **异常处理**：如果plg_uptime > sys_uptime，记录为数据异常，网关重启状态显示为"(-)"
5. **最终统计** → 分别显示真实重启次数、数据异常次数、重启前后内存统计和网关重启统计

---

## 💡 重构建议

这个简化版本将**专注于核心价值**：
- ✅ 智能检测真实重启（过滤数据异常）
- ✅ 简单易用（9个参数）
- ✅ 结果清晰（区分重启和异常）
- ✅ 内存分析（重启前后实际物理内存占用统计，支持配置重启后检测范围）
- ✅ 网关重启关联分析（检测网关与插件同时重启）
- ✅ 扩展列功能（动态获取设备自定义属性信息）
- ✅ 基准时间功能（支持历史时间段分析，便于故障回溯和定期报告）

相比当前400+行的复杂脚本，新版本将是：
- 📦 **更轻量**: ~360行代码（增加内存统计、网关重启检测、扩展列功能和基准时间功能）
- 🧠 **更智能**: 区分真实重启和数据异常
- 🎯 **更准确**: 基于uptime数据特性的检测逻辑
- 📊 **更全面**: 提供重启前后实际物理内存占用分析和网关重启关联分析
- 🔧 **更灵活**: 支持动态扩展列，获取设备自定义属性信息
- 🕐 **更便捷**: 支持基准时间功能，便于历史分析和故障回溯
- 🚀 **更快速**: 使用高效第三方库，无复杂调试和多格式输出
- 📋 **易部署**: 提供requirements.txt，一键安装依赖